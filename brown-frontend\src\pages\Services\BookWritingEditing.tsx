import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  BookOpen,
  Edit3,
  FileText,
  CheckCircle,
  Clock,
  Users,
  Star,
  ArrowRight,
  PenTool,
  Target,
  Award,
  MessageCircle,
} from "lucide-react";

const BookWritingEditing = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Book Writing & Editing Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(50px)",
  });

  const editingServices = [
    {
      title: "Developmental Editing",
      description: "Big-picture feedback on structure, plot, character development, and overall narrative flow.",
      icon: Target,
      features: [
        "Structural analysis and recommendations",
        "Character development feedback",
        "Plot consistency review",
        "Pacing and flow optimization",
        "Theme and message clarity",
      ],
      price: "From $0.08 per word",
    },
    {
      title: "Line Editing",
      description: "Sentence-level improvements for clarity, style, and readability while preserving your voice.",
      icon: Edit3,
      features: [
        "Sentence structure refinement",
        "Style and tone consistency",
        "Voice preservation",
        "Clarity and readability",
        "Flow and rhythm enhancement",
      ],
      price: "From $0.05 per word",
    },
    {
      title: "Copy Editing",
      description: "Grammar, punctuation, spelling, and consistency corrections for polished prose.",
      icon: CheckCircle,
      features: [
        "Grammar and punctuation",
        "Spelling and typos",
        "Consistency in style",
        "Fact-checking",
        "Format standardization",
      ],
      price: "From $0.03 per word",
    },
    {
      title: "Proofreading",
      description: "Final review to catch any remaining errors before publication.",
      icon: FileText,
      features: [
        "Final error detection",
        "Formatting consistency",
        "Layout review",
        "Last-minute corrections",
        "Publication readiness",
      ],
      price: "From $0.02 per word",
    },
  ];

  const writingProcess = [
    {
      step: "1",
      title: "Initial Consultation",
      description: "We discuss your project goals, timeline, and specific needs to create a customized plan.",
      icon: MessageCircle,
    },
    {
      step: "2",
      title: "Project Assessment",
      description: "I review your manuscript or project outline to provide detailed feedback and recommendations.",
      icon: FileText,
    },
    {
      step: "3",
      title: "Collaborative Writing/Editing",
      description: "We work together through multiple rounds of revisions to achieve your vision.",
      icon: Users,
    },
    {
      step: "4",
      title: "Final Polish",
      description: "Final review and refinement to ensure your book is publication-ready.",
      icon: Award,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section ref={heroRef} className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20">
        <div className="container mx-auto px-4">
          <animated.div style={heroSpring} className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <BookOpen className="w-4 h-4" />
              Professional Writing & Editing Services
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Bring Your Story to Life
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              From initial concept to polished manuscript, I provide comprehensive book writing and editing services 
              that honor your unique voice while ensuring professional quality that captivates readers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Start Your Project
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                View Portfolio
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Services Section */}
      <section ref={servicesRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={servicesSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Comprehensive Editing Services
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Every manuscript is unique. I offer tailored editing services to meet your specific needs and budget.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {editingServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <animated.div
                    key={service.title}
                    style={{
                      opacity: servicesInView ? 1 : 0,
                      transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: servicesInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-brand-accent" />
                          </div>
                          <div>
                            <h3 className="text-xl font-serif font-bold text-brand-secondary">
                              {service.title}
                            </h3>
                            <p className="text-brand-accent font-medium">{service.price}</p>
                          </div>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {service.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-brand-secondary/80">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Process Section */}
      <section ref={processRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={processSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                My Writing & Editing Process
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                A collaborative approach that ensures your vision comes to life while maintaining the highest standards.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {writingProcess.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <animated.div
                    key={step.step}
                    style={{
                      opacity: processInView ? 1 : 0,
                      transform: processInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: processInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                    className="text-center"
                  >
                    <div className="relative mb-6">
                      <div className="w-16 h-16 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-8 h-8 text-brand-primary" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-brand-secondary text-brand-primary rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                      {step.title}
                    </h3>
                    <p className="text-brand-secondary/70 text-sm leading-relaxed">
                      {step.description}
                    </p>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Transform Your Manuscript?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              Let's work together to create a book that not only tells your story beautifully but also 
              connects deeply with your readers. Every great book starts with a conversation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Get Your Free Quote
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BookWritingEditing;
