import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Users,
  Target,
  BookOpen,
  Lightbulb,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  MessageCircle,
  TrendingUp,
  Award,
  Heart,
  Zap,
} from "lucide-react";

const CoachingForWriters = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [benefitsRef, benefitsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Writing Coaching Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const benefitsSpring = useSpring({
    opacity: benefitsInView ? 1 : 0,
    transform: benefitsInView ? "translateY(0px)" : "translateY(50px)",
  });

  const coachingServices = [
    {
      title: "One-on-One Coaching",
      description: "Personalized guidance tailored to your unique writing journey and goals.",
      icon: Users,
      features: [
        "Weekly 60-minute sessions",
        "Customized writing exercises",
        "Goal setting and accountability",
        "Manuscript feedback",
        "Publishing strategy guidance",
        "Email support between sessions",
      ],
      price: "$150 per session",
      popular: true,
    },
    {
      title: "Writing Breakthrough Intensive",
      description: "Intensive 3-month program to overcome blocks and establish consistent writing habits.",
      icon: Zap,
      features: [
        "12 weekly coaching sessions",
        "Daily writing accountability",
        "Mindset transformation work",
        "Productivity system setup",
        "Creative block resolution",
        "Community support group",
      ],
      price: "$1,500 for 3 months",
    },
    {
      title: "Manuscript Development Coaching",
      description: "Comprehensive support from concept to completed first draft.",
      icon: BookOpen,
      features: [
        "Story structure guidance",
        "Character development support",
        "Plot problem-solving",
        "Chapter-by-chapter feedback",
        "Revision strategy planning",
        "Publishing preparation",
      ],
      price: "$200 per month",
    },
    {
      title: "Group Coaching Program",
      description: "Small group coaching for writers seeking community and accountability.",
      icon: MessageCircle,
      features: [
        "Monthly group sessions (6-8 writers)",
        "Peer feedback and support",
        "Writing challenges and prompts",
        "Guest expert sessions",
        "Private community access",
        "Resource library",
      ],
      price: "$75 per month",
    },
  ];

  const coachingBenefits = [
    {
      title: "Overcome Writer's Block",
      description: "Break through creative barriers with proven techniques and mindset shifts.",
      icon: Target,
    },
    {
      title: "Develop Your Unique Voice",
      description: "Discover and strengthen your authentic writing style and perspective.",
      icon: Heart,
    },
    {
      title: "Build Consistent Habits",
      description: "Establish sustainable writing routines that fit your lifestyle.",
      icon: Clock,
    },
    {
      title: "Navigate Publishing",
      description: "Get guidance on traditional and self-publishing paths.",
      icon: TrendingUp,
    },
    {
      title: "Gain Confidence",
      description: "Build the confidence to share your work and call yourself a writer.",
      icon: Award,
    },
    {
      title: "Stay Accountable",
      description: "Regular check-ins and support to keep you moving toward your goals.",
      icon: CheckCircle,
    },
  ];

  const testimonials = [
    {
      name: "Sarah M.",
      role: "First-time Author",
      content: "The coaching helped me finish my novel after years of starting and stopping. The accountability and encouragement made all the difference.",
      rating: 5,
    },
    {
      name: "David K.",
      role: "Memoir Writer",
      content: "I learned to trust my voice and tell my story authentically. The feedback was always constructive and inspiring.",
      rating: 5,
    },
    {
      name: "Lisa R.",
      role: "Fiction Writer",
      content: "The manuscript development coaching transformed my scattered ideas into a cohesive, compelling story.",
      rating: 5,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section ref={heroRef} className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20">
        <div className="container mx-auto px-4">
          <animated.div style={heroSpring} className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Users className="w-4 h-4" />
              Writing Coaching & Mentorship
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Unlock Your Writing Potential
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              Transform your writing dreams into reality with personalized coaching that honors your unique voice 
              while providing the structure, accountability, and expertise you need to succeed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Start Your Journey
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Free Discovery Call
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Services Section */}
      <section ref={servicesRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={servicesSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Coaching Programs
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Choose the coaching approach that best fits your needs, goals, and schedule.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {coachingServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <animated.div
                    key={service.title}
                    style={{
                      opacity: servicesInView ? 1 : 0,
                      transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: servicesInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className={`h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 relative ${
                      service.popular ? "ring-2 ring-brand-accent/20" : ""
                    }`}>
                      {service.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-brand-accent text-brand-primary px-4 py-1 rounded-full text-xs font-medium">
                            Most Popular
                          </span>
                        </div>
                      )}
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-brand-accent" />
                          </div>
                          <div>
                            <h3 className="text-xl font-serif font-bold text-brand-secondary">
                              {service.title}
                            </h3>
                            <p className="text-brand-accent font-medium">{service.price}</p>
                          </div>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {service.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3 mb-6">
                          {service.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-brand-secondary/80">{feature}</span>
                            </div>
                          ))}
                        </div>
                        <Button 
                          variant={service.popular ? "hero" : "outline"} 
                          className="w-full group"
                          onClick={scrollToContact}
                        >
                          Get Started
                          <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section ref={benefitsRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={benefitsSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Why Choose Writing Coaching?
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                Personalized support that accelerates your growth and helps you achieve your writing goals faster.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {coachingBenefits.map((benefit, index) => {
                const IconComponent = benefit.icon;
                return (
                  <animated.div
                    key={benefit.title}
                    style={{
                      opacity: benefitsInView ? 1 : 0,
                      transform: benefitsInView ? "translateY(0px)" : "translateY(50px)",
                      transitionDelay: benefitsInView ? `${200 + index * 100}ms` : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-brand-accent/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-brand-accent" />
                    </div>
                    <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                      {benefit.title}
                    </h3>
                    <p className="text-brand-secondary/70 text-sm leading-relaxed">
                      {benefit.description}
                    </p>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Success Stories
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              See how coaching has transformed the writing journeys of authors just like you.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={testimonial.name} className="hover:shadow-elegant transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-brand-accent text-brand-accent" />
                    ))}
                  </div>
                  <p className="text-brand-secondary/80 mb-4 italic">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-brand-secondary">{testimonial.name}</p>
                    <p className="text-sm text-brand-secondary/60">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Transform Your Writing?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              Take the first step toward achieving your writing goals. Schedule a free discovery call 
              to discuss how coaching can accelerate your journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="hero" size="lg" onClick={scrollToContact} className="group">
                Schedule Free Call
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CoachingForWriters;
